

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">
    <meta name="keywords" content="iaas, 云基础结构即服务, active directory, 价格" />
    <meta name="description" content="Azure Active Directory B2C 是一个标识和访问管理云解决方案，适用于面向消费者的 Web 和移动应用。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" />

    <title>Microsoft Entra 外部 ID - Azure云计算</title>

    <link rel="apple-touch-icon" sizes="180x180" href="../../../Static/Favicon/apple-touch-icon.png">
    <link rel="shortcut icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="manifest" href="../../../Static/Favicon/manifest.json">
    <link rel="mask-icon" href="../../../Static/Favicon/safari-pinned-tab.svg" color="#0078D4">
    <meta name="msapplication-config" content="/Static/Favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
        <link rel="canonical" href="https://azure.microsoft.com/zh-cn/pricing/details/microsoft-entra-external-id/"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet">
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet">
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    
    <!-- END: Page Style -->

    <!-- BEGIN: Minified Page Style -->
    <link href='../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css' rel='stylesheet' />
    <!-- END: Minified Page Style -->
            
    <link rel="stylesheet" href="../../../StaticService/css/service.min.css" />
</head>
<body class="zh-cn">    
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    
    <style>
       @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
    </style>
<div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage"></div>
</div>
   
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li><span></span></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select><option selected="selected">加载中...</option></select>
                        <span class="icon icon-arrow-top"></span>
                    </div>
                    
            <tags ms.service="microsoft-entra-external-id" ms.date="09/30/2015" wacn.date="11/27/2015"></tags>
            <style>
                #aad-b2c-table-mau tr,#aad-b2c-table-separate tr{
                    background-color:rgb(255,255,255) !important;
                }
            </style>
            <!-- BEGIN: Product-Detail-TopBanner -->
            <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/activity_directory_b2c.png','imageHeight':'auto'}">
                <div class="common-banner-image">
                    <div class="common-banner-title">
                        <img src="/Images/marketing-resource/media/images/production/<EMAIL>" />
                        <h2>Microsoft Entra 外部 ID 定价</h2>
                        <h4>作为 Microsoft Entra 的一部分，Microsoft Entra 外部 ID 是我们的下一代客户标识和访问管理解决方案(CIAM)，可用于保护和自定义外部标识对应用程序的访问。免费开始使用，只需为所用内容付费。</h4>
                    </div>
                </div>
            </div>
            <!-- END: Product-Detail-TopBanner -->
            <div class="pricing-page-section">
            </div>
            <!-- BEGIN: TAB-CONTROL -->
            <div>
                <h2>定价详细信息</h2>
                <p>你可以免费开始使用 Microsoft Entra 外部 ID，并且在业务增长时只需为使用的内容付费。Microsoft Entra 外部 ID 核心产品/服务的前 50,000 个 MAU 免费，下面列出了完整的定价详细信息:</p>
                <div class="tags-date">
                    <div class="ms-date">*以下价格均为含税价格。</div><br>
                    <div class="ms-date">*每月价格估算基于每个月 31 天的使用量。</div>
                </div>

                <div>
                    <table cellpadding="0" cellspacing="0" id="aad-b2c-table-mau">                        
                        <tr>
                            <th align="left" width="33%"><strong></strong></th>
                            <th align="left" width="33%"><strong>高级版P1

                            </strong></th>
                            <th align="left" width="33%"><strong>高级版P2

                            </strong></th>
                        </tr>
                        <tr>
                            <td>前 50,000 个 MAU</td>
                            <td>￥0/月度活跃用户</td>
                            <td>￥0/月度活跃用户</td>
                            <td rowspan="5"> </td>
                        </tr>
                        <tr>
                            <td>后 50,000 个 MAU</td>
                            <td>￥0.033/月度活跃用户
                          </td>
                            <td>￥0.165/月度活跃用户
                          </td>
                        </tr>     
                    </table>

                    <div>
                        <p>外部 ID 高级功能作为加载项提供。这些高级功能没有免费层。有关详细信息，<a href="https://learn.microsoft.com/zh-cn/entra/external-id/customers/faq-customers" id="faq-customers">请访问常见问题解答</a>。</p>
                    </div>
                </div>

                <br/>
                <div>
                    <h4>外部 ID 加载项</h4>
                    <div class="tags-date">
                        <div class="ms-date">*以下价格均为含税价格。</div><br>
                        <div class="ms-date">*每月价格估算基于每个月 31 天的使用量。</div>
                    </div>
                    
                    <table cellpadding="0" cellspacing="0" width="100%" id="aad-b2c-table-separate">
                        <tr>
                            <th align="left">基于MFA的SMS/电话事件</th>
                            <th align="left">&yen; 0.305 每个 SMS/电话事件
                                <sup>1</sup>
                            </th>
                        </tr>
                    </table>
                    <div class="tags-date">
                        <div class="ms-date">
                        <sup>
                        1
                        </sup>
                        <div class="ms-date">1 个事务 = 1 个 SMS 电话身份验证验证尝试。可在
                            <a href = "https://learn.microsoft.com/en-us/entra/external-id/customers/concept-multifactor-authentication-customers#sms-pricing-tiers-by-countryregion" id="faq-customers">此处</a>
                            找到国家/地区的完整列表。
                        </div>
                        </div>
                    </div>
                </div>

            <!-- END: TAB-CONTROL -->
<!--             <div class="pricing-page-section">
                <h2>支持和服务级别协议</h2>
                <p>如有任何疑问或需要帮助，请访问<a href="https://support.azure.cn/zh-cn/support/contact" id="inentity-contact-page">Azure 支持</a>选择自助服务或者其他任何方式联系我们获得支持。</p>
				 <p>Azure Active Directory 免费版不提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问<a href="../../../support/sla/microsoft-entra-external-id/" id="pricing_identity_sla">服务级别协议</a>页。</p>
            </div> -->
                <!--BEGIN: Support and service code chunk-->
                <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
                <!--END: Support and service code chunk-->
              
            <!--BEGIN: Support and service code chunk-->
            
             
                                                                             
            <!--END: Support and service code chunk-->                                                                                      
    
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->

<!-- BEGIN: Footer -->
<div class="public_footerpage"></div>
<!--END: Common sidebar-->


        
<link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet">
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="4dzSq3xeuKWVc6uR1tjmsjezpcygfV-IQn46j_rGQEl_If8ZwfDEnbVbx_vRG0S7iniIgfOvwYUKMJKlvcbx4G5gXMUu-j0Fu5JDu_cgLiM1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>

<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>

<!-- BEGIN: Minified RequireJs -->
<script src='../../../Static/Scripts/global.config.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/require.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>
<!-- END: Minified RequireJs -->

<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js"></script>

<script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
</script>
<!-- end JSLL -->

<script type="text/javascript" src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../Static/Scripts/wacndatatracker.js"></script>


<script type="text/javascript" src="/common/useCommon.js"></script>

</body>
</html>