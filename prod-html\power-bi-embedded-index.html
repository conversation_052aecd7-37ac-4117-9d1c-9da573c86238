<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Power BI Embedded, 价格详情, 定价, 计费" name="keywords"/>
  <meta content="了解Azure Power Bi Embedded的价格详情。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   Power BI Embedded合数据分析服务定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/power-bi-embedded/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="power-bi-embedded" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/service-banner-power-bi-embedded-slice.png ','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/images/service/power-bi-embedded.png"/>
          <h2>
           Power BI Embedded
          </h2>
          <h4>
           在应用程序中嵌入完整交互式的可视化数据
          </h4>
         </div>
        </div>
       </div>

       <div class="technical-azure-selector pricing-detail-tab tab-dropdown" style="margin-top: 40px">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure AI Search
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_storage-blobs">
                Azure Cognitive
                                                    Search
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                <!-- <option data-href="#tabContent1" selected="selected" value="Azure AI Services"> -->
                <option data-href="#tabContent1" selected="selected" value="Power BI Embedded">
                    Azure AI Services
                </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li>
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#north-china3" selected="selected" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <div class="tab-content">
         <div class="tab-panel" id="tabContent1">
          
       
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section" style="margin-top: 20px;">
        <p>
         Power BI Embedded 允许应用程序开发人员将出色的完全交互式报表和仪表板嵌入应用中，无需花费时间和费用从头开始构建自己的控件。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector" style="min-height: 400px;">
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <h2>
          定价详细信息
         </h2>
         <p>
          Power BI Embedded 的总成本取决于所选择的节点类型和部署的节点数量。节点类型因 V 核心和 RAM 数量而异，如下表中所述：
         </p>
         <div class="tags-date">
          <div class="ms-date">
           *以下价格均为含税价格。
          </div>
          <br/>
         </div>
         <table cellpadding="0" cellspacing="0" width="100%" id="power-bi-embedded-table-a7a8">
          <tr>
           <th align="left">
            <strong>
             节点类型
            </strong>
           </th>
           <!-- <th align="left"><strong>专用容量</strong></th> -->
           <th align="left">
            <strong>
             虚拟内核
            </strong>
           </th>
           <th align="left">
            <strong>
             内存
            </strong>
           </th>
           <th align="left">
            <strong>
             前端/后端内核数
            </strong>
           </th>
           <!-- <th align="left"><strong>峰值呈现数/小时</strong></th> -->
           <th align="left">
            <strong>
             价格
            </strong>
           </th>
          </tr>
          <tr>
           <td>
            A1
           </td>
           <!-- <td>否</td> -->
           <td>
            1
           </td>
           <td>
            3 GB RAM
           </td>
           <td>
            0.5 / 0.5
           </td>
           <!-- <td>300</td> -->
           <td>
            ￥6.4115/小时
            <br/>
            ( 约￥4,770.156/月 )
           </td>
          </tr>
          <tr>
           <td>
            A2
           </td>
           <!-- <td>否</td> -->
           <td>
            2
           </td>
           <td>
            5 GB RAM
           </td>
           <td>
            1 / 1
           </td>
           <!-- <td>600</td> -->
           <td>
            ￥12.7715/小时
            <br/>
            ( 约￥9,501.996/月 )
           </td>
          </tr>
          <tr>
           <td>
            A3
           </td>
           <!-- <td>是</td> -->
           <td>
            4
           </td>
           <td>
            10 GB RAM
           </td>
           <td>
            2 / 2
           </td>
           <!-- <td>1,200</td> -->
           <td>
            ￥25.5939/小时
            <br/>
            ( 约￥19,041.8616/月 )
           </td>
          </tr>
          <tr>
           <td>
            A4
           </td>
           <!-- <td>是</td> -->
           <td>
            8
           </td>
           <td>
            25 GB RAM
           </td>
           <td>
            4 / 4
           </td>
           <!-- <td>2,400</td> -->
           <td>
            ￥51.2393/小时
            <br/>
            ( 约￥38,122.0392/月 )
           </td>
          </tr>
          <tr>
           <td>
            A5
           </td>
           <!-- <td>是</td> -->
           <td>
            16
           </td>
           <td>
            50 GB RAM
           </td>
           <td>
            8 / 8
           </td>
           <!-- <td>4,800</td> -->
           <td>
            ￥102.5296/小时
            <br/>
            ( 约￥76,282.0224/月 )
           </td>
          </tr>
          <tr>
           <td>
            A6
           </td>
           <!-- <td>是</td> -->
           <td>
            32
           </td>
           <td>
            100 GB RAM
           </td>
           <td>
            16 / 16
           </td>
           <!-- <td>9,600</td> -->
           <td>
            ￥205.1138/小时
            <br/>
            ( 约￥152,604.6672/月 )
           </td>
          </tr>
         
         </table>

         <table cellpadding="0" cellspacing="0" width="100%" id="power-bi-embedded-table-hide-a7a8">
            <tr>
             <th align="left">
              <strong>
               节点类型
              </strong>
             </th>
             <!-- <th align="left"><strong>专用容量</strong></th> -->
             <th align="left">
              <strong>
               虚拟内核
              </strong>
             </th>
             <th align="left">
              <strong>
               内存
              </strong>
             </th>
             <th align="left">
              <strong>
               前端/后端内核数
              </strong>
             </th>
             <!-- <th align="left"><strong>峰值呈现数/小时</strong></th> -->
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              A1
             </td>
             <!-- <td>否</td> -->
             <td>
              1
             </td>
             <td>
              3 GB RAM
             </td>
             <td>
              0.5 / 0.5
             </td>
             <!-- <td>300</td> -->
             <td>
              ￥6.4115/小时
              <br/>
              ( 约￥4,770.156/月 )
             </td>
            </tr>
            <tr>
             <td>
              A2
             </td>
             <!-- <td>否</td> -->
             <td>
              2
             </td>
             <td>
              5 GB RAM
             </td>
             <td>
              1 / 1
             </td>
             <!-- <td>600</td> -->
             <td>
              ￥12.7715/小时
              <br/>
              ( 约￥9,501.996/月 )
             </td>
            </tr>
            <tr>
             <td>
              A3
             </td>
             <!-- <td>是</td> -->
             <td>
              4
             </td>
             <td>
              10 GB RAM
             </td>
             <td>
              2 / 2
             </td>
             <!-- <td>1,200</td> -->
             <td>
              ￥25.5939/小时
              <br/>
              ( 约￥19,041.8616/月 )
             </td>
            </tr>
            <tr>
             <td>
              A4
             </td>
             <!-- <td>是</td> -->
             <td>
              8
             </td>
             <td>
              25 GB RAM
             </td>
             <td>
              4 / 4
             </td>
             <!-- <td>2,400</td> -->
             <td>
              ￥51.2393/小时
              <br/>
              ( 约￥38,122.0392/月 )
             </td>
            </tr>
            <tr>
             <td>
              A5
             </td>
             <!-- <td>是</td> -->
             <td>
              16
             </td>
             <td>
              50 GB RAM
             </td>
             <td>
              8 / 8
             </td>
             <!-- <td>4,800</td> -->
             <td>
              ￥102.5296/小时
              <br/>
              ( 约￥76,282.0224/月 )
             </td>
            </tr>
            <tr>
             <td>
              A6
             </td>
             <!-- <td>是</td> -->
             <td>
              32
             </td>
             <td>
              100 GB RAM
             </td>
             <td>
              16 / 16
             </td>
             <!-- <td>9,600</td> -->
             <td>
              ￥205.1138/小时
              <br/>
              ( 约￥152,604.6672/月 )
             </td>
            </tr>
            <tr>
              <td>
               A7
              </td>
              <!-- <td>是</td> -->
              <td>
               64
              </td>
              <td>
               200 GB RAM
              </td>
              <td>
               32 / 32
              </td>
              <!-- <td>9,600</td> -->
              <td>
               ￥410.23/小时
               <br/>
               ( 约￥305,211.12/月 )
              </td>
             </tr>
             <tr>
              <td>
               A8
              </td>
              <!-- <td>是</td> -->
              <td>
               128
              </td>
              <td>
               400 GB RAM
              </td>
              <td>
               64 / 64
              </td>
              <!-- <td>9,600</td> -->
              <td>
               ￥820.47/小时
               <br/>
               ( 约￥602,989.68/月 )
              </td>
             </tr>
           </table>
         <!-- <div class="tags-date">
                        <p>查看 Power BI 工作区集合的定价。</p>
                        <div class="tags-date">
                            <div class="ms-date">*以下价格均为含税价格。</div><br />                        
                        </div>
                        
                        <table cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <th align="left" width="35%"><strong></strong></th>
                                <th align="left"><strong>免费</strong></th>
                                <th align="left"><strong>标准</strong></th>
                            </tr>                     
                           <tr>                         
                               <td>按会话定价 （每 100 个会话）</td>
                               <td>每月前 100 个会话免费</td>
                               <td>￥ 32.86 /月</td>
                           </tr>
                        </table>
                        <div class="tags-date">
                            <div class="ms-date">*Power BI 工作区集合将在 2019 年 6 月 30 日停用。了解更多关于<a style="font-size:12px" href="https://docs.azure.cn/zh-cn/power-bi-embedded/migrate-from-power-bi-workspace-collections">如何将 Power BI 工作区集合内容迁移到 Power BI Embedded</a></div><br />                        
                        </div>
						<p>在下述应用程序中使用 Power BI Embedded 服务：这些程序向服务中增添主要及重要功能，它们从根本上而言并非任何 Power BI 服务的替代，并且它们供第三方使用。</p>
                    </div> -->
         <!-- END: Table1-Content-->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
         </div>
        </div>
      </div>

       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="power-bi-embedded_faq_que1">
             多久可更改已部署的节点类型？
            </a>
            <section>
             <p>
              客户可根据需要更改部署的节点类型。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="power-bi-embedded_faq_que2">
             Power BI Embedded 的使用情况如何体现在我的帐单上？
            </a>
            <section>
             <p>
              Power BI Embedded 根据部署的节点类型按可预测的小时费率计费。实际用量计算到秒，并按小时收费。例如，如果某个实例一个月内运行了 12 小时 15 分钟，则账单会显示使用了 12.25 小时。如果实例仅有 6 分钟处于活动状态，则账单会显示使用了 0.1 小时。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="power-bi-embedded_faq_que3">
             若要将 BI 发布到 Power BI Embedded 节点中，是否还需要购买任何其他许可证？
            </a>
            <section>
             <p>
              是，发布 BI 内容的用户需要由 Power BI Pro 授权。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="power-bi-embedded_faq_que4">
             是否需要向通过 Power BI Embedded 查看已发布 BI 的用户单独授权？
            </a>
            <section>
             <p>
              否，不需要向查看内容的用户分配 Power BI 许可证。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="power-bi-embedded_faq_que5">
             暂停服务后会发生什么？
            </a>
            <section>
             <p>
              服务暂停时，嵌入的内容不会加载，也不会针对该服务向你收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="power-bi-embedded_faq_que6">
             Power BI Embedded 与 Power BI 服务有什么关系？
            </a>
            <section>
             <p>
              Power BI Embedded 适用于生成应用程序并想要使用第三方 BI 服务/产品对应用程序数据进行可视化（而非自行构建应用程序数据）的开发人员。开发人员使用 Power BI Embedded 将仪表板和报表功率嵌入应用程序。另一方面，Power BI 是一个服务型软件分析解决方案，为组织提供其最关键业务数据的单一视图。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="power-bi-embedded_faq_que7">
             Power BI Embedded 可以用于创建内部应用程序吗？
            </a>
            <section>
             <p>
              可以，但如果打算使用 Power BI SaaS 嵌入，则需要通过 Office 使用 Power BI Premium（EM1-EM3、P1-P3）。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>Power BI 工作区集合在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table> -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="pricing_power-bi-embedded_contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证，用户执行  API  调用和嵌入报告时，Power BI Embedded 的可用性不低于 99.9%。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/power-bi-embedded/index.html" id="pricing_power-bi-embedded_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--  

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
         -->
       <!-- BEGIN: Product-Detail-BottomBanner -->
       <!-- END: Product-Detail-BottomBanner -->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  
<script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
</script>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="aWfR98AYkaRrwA_p1HcZD-ZCedK7oMa6gsJVByqkNgYqTI6KBvkQ7pVRUxHN6GIgAaYTov4cEqfEgcfsA45FsYibiCzHlI7XL3yYUpnwZrs1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
</script>
<!-- <script src='/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js' type='text/javascript'></script> -->
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
 </body>
</html>
