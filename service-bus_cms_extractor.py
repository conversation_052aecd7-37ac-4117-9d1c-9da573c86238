#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用产品页面CMS提取器
适用于没有区域筛选的Azure产品页面，如Service Bus等
基于现有的MySQL提取器架构，专门用于CMS系统导入
"""

import json
import os
import argparse
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup, Comment, NavigableString, Tag


class GenericProductCMSExtractor:
    """通用产品页面CMS HTML提取器"""
    
    def __init__(self, output_dir: str = "cms_output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.original_soup = None
        
        print(f"✓ 通用产品页面CMS提取器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
    
    def extract_product_cms_html(self, html_file_path: str, product_name: str = "") -> Dict[str, any]:
        """提取产品页面的CMS友好HTML"""
        
        print(f"\n🔧 开始提取产品页面CMS HTML")
        print(f"📁 源文件: {html_file_path}")
        print(f"🏷️ 产品名称: {product_name}")
        print("=" * 70)
        
        if not os.path.exists(html_file_path):
            raise FileNotFoundError(f"HTML文件不存在: {html_file_path}")
        
        start_time = datetime.now()
        
        try:
            # 1. 加载和解析HTML
            with open(html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            self.original_soup = BeautifulSoup(html_content, 'html.parser')
            print(f"✓ HTML文件加载成功，大小: {len(html_content):,} 字符")
            
            # 2. 自动识别产品信息
            if not product_name:
                product_name = self._auto_detect_product_name(soup)
            
            # 3. 提取和清洗内容
            cleaned_soup = self._extract_and_clean_product_content(soup)
            
            # 4. 进一步清洗以适应CMS
            cms_ready_soup = self._prepare_product_for_cms(cleaned_soup, product_name)
            
            # 5. 生成最终HTML
            final_html = self._build_final_product_html(cms_ready_soup, product_name)
            
            # 6. 验证结果
            verification = self._verify_product_extraction_result(final_html, product_name)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                "success": True,
                "product_name": product_name,
                "html_content": final_html,
                "statistics": {
                    "original_size": len(html_content),
                    "final_size": len(final_html),
                    "compression_ratio": round(len(final_html) / len(html_content), 3),
                    "processing_time": processing_time
                },
                "verification": verification,
                "extraction_info": {
                    "source_file": html_file_path,
                    "extracted_at": start_time.isoformat(),
                    "extraction_method": "generic_product_cms",
                    "version": "1.0_generic"
                }
            }
            
            print(f"\n✅ 产品页面CMS HTML提取完成！")
            print(f"📄 压缩比: {result['statistics']['compression_ratio']*100:.1f}%")
            print(f"📊 内容组件: {verification['content_components']}")
            print(f"⏱️ 处理时间: {processing_time:.2f}秒")
            
            return result
            
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e),
                "product_name": product_name
            }
    
    def _auto_detect_product_name(self, soup: BeautifulSoup) -> str:
        """自动检测产品名称"""
        
        # 从title标签检测
        title_tag = soup.find('title')
        if title_tag:
            title_text = title_tag.get_text(strip=True)
            # 提取第一个 - 之前的部分作为产品名
            if ' - ' in title_text:
                product_name = title_text.split(' - ')[0].strip()
                print(f"📝 从页面标题检测到产品: {product_name}")
                return product_name
        
        # 从产品横幅检测
        banner = soup.find('div', class_='common-banner')
        if banner:
            h2 = banner.find('h2')
            if h2:
                # 提取中文产品名
                product_text = h2.get_text(strip=True)
                # 如果包含英文名，只取中文部分
                if '\n' in product_text:
                    lines = [line.strip() for line in product_text.split('\n') if line.strip()]
                    product_name = lines[0] if lines else "未知产品"
                else:
                    product_name = product_text.split()[0] if product_text else "未知产品"
                print(f"📝 从产品横幅检测到产品: {product_name}")
                return product_name
        
        # 从meta description检测
        meta_desc = soup.find('meta', {'name': 'description'})
        if meta_desc:
            desc_content = meta_desc.get('content', '')
            if 'Azure' in desc_content:
                # 尝试提取Azure后面的产品名
                match = re.search(r'Azure\s+([^（\(]+)', desc_content)
                if match:
                    product_name = match.group(1).strip()
                    print(f"📝 从meta描述检测到产品: {product_name}")
                    return product_name
        
        return "未知产品"
    
    def _extract_and_clean_product_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """提取和清洗产品内容"""
        
        print("🧹 第一步：提取和清洗产品内容...")
        
        # 复制整个soup，然后进行清洗
        cleaned_soup = BeautifulSoup(str(soup), 'html.parser')
        
        # 检查点1：初始状态
        initial_containers = self._check_pricing_containers(cleaned_soup, "初始状态")
        
        # 1. 移除不需要的元素
        print("  🗑️ 移除不需要的元素...")
        self._remove_unwanted_elements(cleaned_soup)
        self._check_pricing_containers(cleaned_soup, "移除不需要元素后")
        
        # 2. 清洗样式和脚本
        print("  🎨 清洗样式和脚本...")
        self._clean_styles_and_scripts(cleaned_soup)
        self._check_pricing_containers(cleaned_soup, "清洗样式脚本后")
        
        # 3. 移除导航和交互元素
        print("  🧭 移除导航和交互元素...")
        self._remove_navigation_elements(cleaned_soup)
        self._check_pricing_containers(cleaned_soup, "移除导航元素后")
        
        # 4. 展开tab结构，保留所有内容
        print("  📂 展开tab结构...")
        self._flatten_tab_structures(cleaned_soup)
        containers_after_flatten = self._check_pricing_containers(cleaned_soup, "展开tab结构后")
        
        # 5. 清理属性但保留内容
        print("  🧹 清理属性...")
        self._clean_attributes_keep_content(cleaned_soup)
        self._check_pricing_containers(cleaned_soup, "清理属性后")
        
        # 6. 提取主要内容区域
        print("  🎯 提取主要内容区域...")
        main_content = self._extract_main_product_content_area(cleaned_soup)
        
        print("  ✓ 产品内容提取和清洗完成")
        
        return main_content
    
    def _check_pricing_containers(self, soup: BeautifulSoup, stage: str) -> int:
        """检查定价容器是否还存在"""
        
        tech_containers = soup.select('div[class*="technical-azure-selector"]')
        tab_containers = soup.select('div[class*="tab-control-container"]')
        
        total_containers = len(tech_containers) + len(tab_containers)
        tables_count = len(soup.find_all('table'))
        
        print(f"    🔍 检查点 - {stage}:")
        print(f"        technical-azure-selector: {len(tech_containers)} 个")
        print(f"        tab-control-container: {len(tab_containers)} 个")
        print(f"        总表格数: {tables_count}")
        
        if total_containers == 0 and stage != "初始状态":
            print(f"    ⚠️ 警告: 在 {stage} 阶段丢失了定价容器!")
        
        return total_containers
    
    def _remove_unwanted_elements(self, soup: BeautifulSoup):
        """移除不需要的元素"""
        
        # 移除脚本和样式相关
        unwanted_selectors = [
            'script', 'noscript', 'style', 'link[rel="stylesheet"]',
            'meta[http-equiv]', 'meta[name="viewport"]'
        ]
        
        for selector in unwanted_selectors:
            for element in soup.select(selector):
                element.decompose()
        
        # 移除注释
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
    
    def _clean_styles_and_scripts(self, soup: BeautifulSoup):
        """清理样式和脚本"""
        
        # 移除内联样式属性
        for tag in soup.find_all():
            if tag.get('style'):
                del tag['style']
            
            # 移除事件处理器
            attrs_to_remove = []
            for attr in tag.attrs:
                if attr.startswith('on'):  # onclick, onload, etc.
                    attrs_to_remove.append(attr)
            
            for attr in attrs_to_remove:
                del tag[attr]
    
    def _remove_navigation_elements(self, soup: BeautifulSoup):
        """移除导航和交互元素，但保护重要的内容容器"""
        
        # 移除导航相关的class和元素 - 但要排除重要的内容容器
        navigation_classes = [
            'bread-crumb', 'left-navigation-select', 'documentation-navigation',
            'acn-header-container', 'public_footerpage', 'loader'
        ]
        
        # 重要：绝对不能删除的class
        protected_classes = [
            'technical-azure-selector', 'tab-control-container', 'tab-control-selector',
            'tab-active', 'pricing-page-section', 'more-detail', 'pure-content'
        ]
        
        removed_count = 0
        
        for class_name in navigation_classes:
            elements = soup.find_all(class_=class_name)
            for element in elements:
                # 双重检查：确保不删除包含重要内容的元素
                element_classes = element.get('class', [])
                has_protected_class = any(
                    protected_class in element_classes 
                    for protected_class in protected_classes
                )
                
                if not has_protected_class:
                    element.decompose()
                    removed_count += 1
                else:
                    print(f"    🛡️ 保护元素: {' '.join(element_classes)} (导航清理)")
        
        # 移除导航标签
        for tag in soup.find_all(['nav', 'header', 'footer']):
            # 检查是否包含重要内容
            if tag.find_all('table') or tag.select('div[class*="technical-azure-selector"]') or tag.select('div[class*="tab-control"]'):
                print(f"    🛡️ 保护包含重要内容的 {tag.name} 标签")
                continue
            tag.decompose()
            removed_count += 1
        
        # 移除表单元素
        for tag in soup.find_all(['form', 'input', 'select', 'option', 'button', 'textarea']):
            tag.decompose()
            removed_count += 1
        
        print(f"    移除了 {removed_count} 个导航/交互元素")
    
    def _flatten_tab_structures(self, soup: BeautifulSoup):
        """展开tab结构，保留所有内容"""
        
        print("    📂 展开tab结构，保留所有内容...")
        
        # 移除tab导航，但保留内容
        for tab_nav in soup.find_all('ul', class_='tab-nav'):
            tab_nav.decompose()
        
        # 展开tab内容面板
        tab_containers = soup.find_all('div', class_=lambda x: x and any(
            keyword in ' '.join(x) for keyword in ['tab-control', 'technical-azure-selector']
        ))
        
        for container in tab_containers:
            if container.parent:
                children = list(container.children)
                for child in children:
                    if hasattr(child, 'extract'):
                        child.extract()
                        container.insert_before(child)
                container.decompose()
    
    def _clean_attributes_keep_content(self, soup: BeautifulSoup):
        """清理属性但保留内容结构和重要的定价class"""
        
        # 要保留的重要属性
        important_attrs = {'id', 'class', 'href', 'src', 'alt', 'title', 'colspan', 'rowspan'}
        
        # 要保留的重要class - 包含定价相关的关键class
        important_classes = {
            'common-banner', 'common-banner-image', 'common-banner-title',
            'pricing-page-section', 'more-detail', 'tags-date', 'ms-date',
            # 🔥 关键修正：添加定价相关的重要class
            'technical-azure-selector', 'tab-control-container', 'tab-control-selector',
            'tab-active', 'pure-content', 'tab-panel', 'tab-content'
        }
        
        print(f"    清理属性，保护 {len(important_classes)} 个重要class...")
        
        for tag in soup.find_all():
            if not hasattr(tag, 'attrs'):
                continue
                
            # 移除不重要的属性
            attrs_to_remove = []
            for attr in tag.attrs:
                if attr not in important_attrs:
                    attrs_to_remove.append(attr)
            
            for attr in attrs_to_remove:
                del tag[attr]
            
            # 过滤class属性 - 但要保留重要的定价class
            if tag.get('class'):
                current_classes = tag['class'] if isinstance(tag['class'], list) else [tag['class']]
                filtered_classes = [cls for cls in current_classes if cls in important_classes]
                
                # 🔥 关键修正：如果有重要class就保留，而不是删除
                if filtered_classes:
                    tag['class'] = filtered_classes
                else:
                    # 只有当没有任何重要class时才删除class属性
                    if 'class' in tag.attrs:
                        del tag['class']
        
        print(f"    属性清理完成")
    
    def _extract_main_product_content_area(self, soup: BeautifulSoup) -> BeautifulSoup:
        """按正确顺序提取主要产品内容区域"""
        
        print("    🎯 按顺序提取主要产品内容...")
        
        # 创建新的内容容器
        content_soup = BeautifulSoup("", 'html.parser')
        
        # 查找主要内容区域
        main_content_areas = [
            soup.find('div', class_='pure-content'),
            soup.find('main'),
            soup.find('div', class_='content'),
            soup.find('body')
        ]
        
        main_area = None
        for area in main_content_areas:
            if area:
                main_area = area
                break
        
        if not main_area:
            main_area = soup
        
        # 按正确顺序提取结构化内容
        content_elements = []
        
        # 1. 提取产品横幅
        print("      🏠 提取产品横幅...")
        banner = main_area.find('div', class_='common-banner')
        if banner:
            banner_element = self._create_clean_product_banner(banner, content_soup)
            if banner_element:
                content_elements.append(banner_element)
                print(f"        ✓ 产品横幅已提取")
        
        # 2. 提取产品介绍部分
        print("      📝 提取产品介绍...")
        intro_count = self._extract_product_introduction(main_area, content_soup, content_elements)
        print(f"        ✓ 提取了 {intro_count} 个产品介绍组件")
        
        # 3. 提取定价详细信息（最重要的部分）
        print("      💰 提取定价详细信息...")
        pricing_count = self._extract_pricing_details(main_area, content_soup, content_elements)
        print(f"        ✓ 提取了 {pricing_count} 个定价组件")
        
        # 4. 提取常见问题
        print("      ❓ 提取FAQ常见问题...")
        faq_count = self._extract_faq_section(main_area, content_soup, content_elements)
        print(f"        ✓ 提取了 {faq_count} 个FAQ组件")
        
        # 5. 提取支持和SLA信息
        print("      🛠️ 提取支持和SLA信息...")
        support_count = self._extract_support_sla_section(main_area, content_soup, content_elements)
        print(f"        ✓ 提取了 {support_count} 个支持组件")
        
        # 将所有内容元素按顺序添加到soup中
        for element in content_elements:
            content_soup.append(element)
        
        elements_count = len(content_elements)
        print(f"    ✓ 总计提取了 {elements_count} 个内容组件（按正确顺序）")
        
        return content_soup
    
    def _extract_product_introduction(self, main_area: Tag, soup: BeautifulSoup, content_elements: List[Tag]) -> int:
        """提取产品介绍部分"""
        
        intro_count = 0
        
        # 查找第一个 pricing-page-section（通常是产品介绍）
        pricing_sections = main_area.find_all('div', class_='pricing-page-section')
        
        for i, section in enumerate(pricing_sections):
            # 跳过包含FAQ的部分
            if section.find('div', class_='more-detail'):
                continue
            
            # 检查是否包含支持信息（通常在最后）
            text_content = section.get_text(strip=True)
            if any(keyword in text_content for keyword in ['支持', '服务级别协议', 'SLA', '联系我们']):
                continue  # 这部分留给支持信息处理
            
            # 这应该是产品介绍部分
            intro_section = soup.new_tag('section', **{'class': 'product-intro'})
            
            # 提取标题
            for heading in section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'], recursive=False):
                if heading.get_text(strip=True):
                    new_heading = soup.new_tag(heading.name)
                    new_heading.string = heading.get_text(strip=True)
                    intro_section.append(new_heading)
            
            # 提取段落
            for p in section.find_all('p', recursive=False):
                if p.get_text(strip=True):
                    new_p = soup.new_tag('p')
                    if p.find('a'):
                        self._copy_paragraph_with_links(p, new_p, soup)
                    else:
                        new_p.string = p.get_text(strip=True)
                    intro_section.append(new_p)
            
            if intro_section.children:
                content_elements.append(intro_section)
                intro_count += 1
                print(f"        📝 添加产品介绍部分 {i}")
                break  # 通常只有第一个是产品介绍
        
        return intro_count
    
    def _extract_pricing_details(self, main_area: Tag, soup: BeautifulSoup, content_elements: List[Tag]) -> int:
        """提取定价详细信息"""
        
        pricing_count = 0
        
        # 使用CSS选择器查找定价容器
        tech_selectors = main_area.select('div[class*="technical-azure-selector"]')
        tab_containers = main_area.select('div[class*="tab-control-container"]')
        
        print(f"        🔍 找到定价容器:")
        print(f"            technical-azure-selector: {len(tech_selectors)} 个")
        print(f"            tab-control-container: {len(tab_containers)} 个")
        
        # 合并并去重
        all_containers = tech_selectors + tab_containers
        unique_containers = []
        for container in all_containers:
            if container not in unique_containers:
                unique_containers.append(container)
        
        for i, container in enumerate(unique_containers):
            classes = ' '.join(container.get('class', []))
            tables_count = len(container.find_all('table'))
            
            print(f"        📦 处理定价容器 {i}: {classes}")
            print(f"            包含表格: {tables_count} 个")
            
            if tables_count > 0:
                pricing_content = self._extract_detailed_pricing_content(container, soup)
                if pricing_content:
                    content_elements.extend(pricing_content)
                    pricing_count += len(pricing_content)
                    print(f"            ✅ 成功提取 {len(pricing_content)} 个定价组件")
                else:
                    print(f"            ❌ 提取失败")
            else:
                print(f"            ⚠️ 容器中没有表格")
        
        return pricing_count
    
    def _extract_faq_section(self, main_area: Tag, soup: BeautifulSoup, content_elements: List[Tag]) -> int:
        """提取FAQ常见问题部分"""
        
        faq_count = 0
        
        # 查找FAQ部分
        faq_section = main_area.find('div', class_='more-detail')
        if faq_section:
            faq_content = self._extract_faq_content(faq_section, soup)
            if faq_content:
                # 包装在FAQ section中
                faq_wrapper = soup.new_tag('section', **{'class': 'faq-section'})
                for faq_item in faq_content:
                    faq_wrapper.append(faq_item)
                
                content_elements.append(faq_wrapper)
                faq_count = len(faq_content)
                print(f"        ❓ 添加FAQ部分，包含 {faq_count} 个问答")
        
        return faq_count
    
    def _extract_support_sla_section(self, main_area: Tag, soup: BeautifulSoup, content_elements: List[Tag]) -> int:
        """提取支持和SLA信息部分"""
        
        support_count = 0
        
        # 查找包含支持信息的 pricing-page-section
        pricing_sections = main_area.find_all('div', class_='pricing-page-section')
        
        for section in pricing_sections:
            text_content = section.get_text(strip=True)
            
            # 检查是否包含支持相关关键词
            if any(keyword in text_content for keyword in ['支持', '服务级别协议', 'SLA', '联系', '服务热线']):
                sla_section = soup.new_tag('section', **{'class': 'sla-section'})
                
                # 提取标题
                for heading in section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                    if heading.get_text(strip=True):
                        new_heading = soup.new_tag('h3', **{'class': 'sla-title'})
                        new_heading.string = heading.get_text(strip=True)
                        sla_section.append(new_heading)
                
                # 提取段落
                for p in section.find_all('p'):
                    if p.get_text(strip=True):
                        new_p = soup.new_tag('div', **{'class': 'sla-content'})
                        if p.find('a'):
                            self._copy_paragraph_with_links(p, new_p, soup)
                        else:
                            new_p.string = p.get_text(strip=True)
                        sla_section.append(new_p)
                
                if sla_section.children:
                    content_elements.append(sla_section)
                    support_count += 1
                    print(f"        🛠️ 添加支持和SLA部分")
        
        return support_count
    
    def _create_clean_product_banner(self, original_banner: Tag, soup: BeautifulSoup) -> Optional[Tag]:
        """创建清洁的产品横幅"""
        
        banner_section = soup.new_tag('section', **{'class': 'product-banner'})
        
        # 提取产品图标
        img = original_banner.find('img')
        if img and img.get('src'):
            icon_div = soup.new_tag('div', **{'class': 'product-icon'})
            new_img = soup.new_tag('img')
            new_img['src'] = img['src']
            new_img['alt'] = 'Product Icon'
            icon_div.append(new_img)
            banner_section.append(icon_div)
        
        # 提取标题
        h2 = original_banner.find('h2')
        if h2:
            title_div = soup.new_tag('div', **{'class': 'product-title'})
            
            # 处理产品名称和英文名
            title_text = h2.get_text('\n', strip=True)
            lines = [line.strip() for line in title_text.split('\n') if line.strip()]
            
            if lines:
                # 主标题（中文）
                main_title = soup.new_tag('h1')
                main_title.string = lines[0]
                title_div.append(main_title)
                
                # 英文副标题
                if len(lines) > 1:
                    sub_title = soup.new_tag('span', **{'class': 'english-name'})
                    sub_title.string = lines[1]
                    title_div.append(sub_title)
            
            banner_section.append(title_div)
        
        # 提取描述
        h4 = original_banner.find('h4')
        if h4:
            desc_div = soup.new_tag('div', **{'class': 'product-description'})
            desc_div.string = h4.get_text(strip=True)
            banner_section.append(desc_div)
        
        return banner_section if banner_section.children else None
    
    def _extract_pricing_section_content(self, section: Tag, soup: BeautifulSoup, section_index: int) -> List[Tag]:
        """提取定价页面部分内容（产品介绍、支持信息等）"""
        
        content_elements = []
        
        # 检查是否包含FAQ（跳过，因为会单独处理）
        if section.find('div', class_='more-detail'):
            return content_elements
        
        # 为不同部分添加合适的标识
        section_class = "product-intro" if section_index == 0 else "support-info"
        section_wrapper = soup.new_tag('section', **{'class': section_class})
        
        # 提取标题
        for heading in section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'], recursive=False):
            if heading.get_text(strip=True):
                new_heading = soup.new_tag(heading.name)
                new_heading.string = heading.get_text(strip=True)
                section_wrapper.append(new_heading)
        
        # 提取段落（包含链接的段落）
        for p in section.find_all('p', recursive=False):
            if p.get_text(strip=True):
                new_p = soup.new_tag('p')
                # 处理段落中的链接
                if p.find('a'):
                    self._copy_paragraph_with_links(p, new_p, soup)
                else:
                    new_p.string = p.get_text(strip=True)
                section_wrapper.append(new_p)
        
        # 提取列表
        for ul in section.find_all(['ul', 'ol'], recursive=False):
            new_list = soup.new_tag(ul.name)
            for li in ul.find_all('li'):
                new_li = soup.new_tag('li')
                new_li.string = li.get_text(strip=True)
                new_list.append(new_li)
            if new_list.find_all('li'):
                section_wrapper.append(new_list)
        
        if section_wrapper.children:
            content_elements.append(section_wrapper)
        
        return content_elements
    
    def _extract_detailed_pricing_content(self, container: Tag, soup: BeautifulSoup) -> List[Tag]:
        """提取详细定价内容（tab-control-container中的复杂内容）"""
        
        content_elements = []
        
        # 查找实际的tab内容容器
        tab_content = container.find('div', class_='tab-control-container')
        if not tab_content:
            tab_content = container
        
        # 创建定价详情部分
        pricing_section = soup.new_tag('section', **{'class': 'pricing-details'})
        
        # 按顺序处理所有子元素，保持内容的完整性和顺序
        current_subsection = None
        
        for element in tab_content.children:
            if not hasattr(element, 'name') or not element.name:
                continue
            
            # 处理标题 - 创建新的子部分
            if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                title_text = element.get_text(strip=True)
                if title_text:
                    # 如果有当前子部分，先添加到主部分
                    if current_subsection and current_subsection.children:
                        pricing_section.append(current_subsection)
                    
                    # 创建新的子部分
                    current_subsection = soup.new_tag('div', **{'class': 'pricing-subsection'})
                    new_heading = soup.new_tag(element.name)
                    new_heading.string = title_text
                    current_subsection.append(new_heading)
            
            # 处理段落
            elif element.name == 'p':
                text_content = element.get_text(strip=True)
                if text_content:
                    new_p = soup.new_tag('p')
                    # 处理包含链接的段落
                    if element.find('a'):
                        self._copy_paragraph_with_links(element, new_p, soup)
                    else:
                        new_p.string = text_content
                    
                    if current_subsection is not None:
                        current_subsection.append(new_p)
                    else:
                        pricing_section.append(new_p)
            
            # 处理表格
            elif element.name == 'table':
                clean_table = self._clean_pricing_table(element, soup)
                if clean_table:
                    if current_subsection is not None:
                        current_subsection.append(clean_table)
                    else:
                        pricing_section.append(clean_table)
            
            # 处理日期标签和备注
            elif element.name == 'div' and 'tags-date' in element.get('class', []):
                notes = self._extract_date_tags_content(element, soup)
                for note in notes:
                    if current_subsection is not None:
                        current_subsection.append(note)
                    else:
                        pricing_section.append(note)
        
        # 添加最后一个子部分
        if current_subsection and current_subsection.children:
            pricing_section.append(current_subsection)
        
        if pricing_section.children:
            content_elements.append(pricing_section)
        
        return content_elements
    
    def _copy_paragraph_with_links(self, original_p: Tag, new_p: Tag, soup: BeautifulSoup):
        """复制包含链接的段落"""
        
        for child in original_p.children:
            if hasattr(child, 'name') and child.name == 'a':
                # 复制链接
                new_link = soup.new_tag('a')
                new_link['href'] = child.get('href', '#')
                new_link.string = child.get_text(strip=True)
                new_p.append(new_link)
            elif hasattr(child, 'strip'):
                # 复制文本
                text = child.strip()
                if text:
                    new_p.append(text)
    
    def _clean_pricing_table(self, original_table: Tag, soup: BeautifulSoup) -> Optional[Tag]:
        """清洗定价表格"""
        
        if not original_table or not original_table.find_all('tr'):
            return None
        
        # 创建新表格
        new_table = soup.new_tag('table', **{'class': 'pricing-table'})
        
        # 复制表格内容
        for row in original_table.find_all('tr'):
            new_row = soup.new_tag('tr')
            
            for cell in row.find_all(['td', 'th']):
                cell_name = 'th' if cell.name == 'th' else 'td'
                new_cell = soup.new_tag(cell_name)
                
                # 保留重要属性
                for attr in ['colspan', 'rowspan', 'align']:
                    if cell.get(attr):
                        new_cell[attr] = cell[attr]
                
                # 处理单元格内容
                cell_content = self._extract_cell_content(cell, soup)
                if cell_content:
                    new_cell.extend(cell_content)
                else:
                    cell_text = cell.get_text(strip=True)
                    if cell_text:
                        new_cell.string = cell_text
                
                new_row.append(new_cell)
            
            new_table.append(new_row)
        
        return new_table
    
    def _extract_cell_content(self, cell: Tag, soup: BeautifulSoup) -> List:
        """提取单元格内容，包括图标等"""
        
        content = []
        
        for child in cell.children:
            if hasattr(child, 'name'):
                if child.name == 'i' and 'icon-tick' in child.get('class', []):
                    # 转换勾选图标为文本
                    content.append('✓')
                elif child.name == 'strong':
                    strong_tag = soup.new_tag('strong')
                    strong_tag.string = child.get_text(strip=True)
                    content.append(strong_tag)
                elif child.name == 'sup':
                    sup_tag = soup.new_tag('sup')
                    sup_tag.string = child.get_text(strip=True)
                    content.append(sup_tag)
                elif child.name == 'p':
                    p_tag = soup.new_tag('p')
                    p_tag.string = child.get_text(strip=True)
                    content.append(p_tag)
            elif hasattr(child, 'strip'):
                text = child.strip()
                if text:
                    content.append(text)
        
        return content
    
    def _extract_date_tags_content(self, date_div: Tag, soup: BeautifulSoup) -> List[Tag]:
        """提取日期标签内容"""
        
        content_elements = []
        
        for ms_date in date_div.find_all('div', class_='ms-date'):
            if ms_date.get_text(strip=True):
                note_p = soup.new_tag('p', **{'class': 'pricing-note'})
                note_p.string = ms_date.get_text(strip=True)
                content_elements.append(note_p)
        
        return content_elements
    
    def _extract_faq_content(self, faq_section: Tag, soup: BeautifulSoup) -> List[Tag]:
        """提取FAQ内容"""
        
        content_elements = []
        
        # 添加FAQ标题
        h2 = faq_section.find('h2')
        if h2:
            faq_title = soup.new_tag('h2', **{'class': 'faq-title'})
            faq_title.string = h2.get_text(strip=True)
            content_elements.append(faq_title)
        
        # 处理FAQ项目
        faq_items = faq_section.find_all('li')
        for item in faq_items:
            faq_item_div = soup.new_tag('div', **{'class': 'faq-item'})
            
            # 提取问题
            question_link = item.find('a')
            if question_link:
                question_div = soup.new_tag('div', **{'class': 'faq-question'})
                question_div.string = question_link.get_text(strip=True)
                faq_item_div.append(question_div)
            
            # 提取答案
            answer_section = item.find('section')
            if answer_section:
                answer_div = soup.new_tag('div', **{'class': 'faq-answer'})
                
                # 处理答案中的各种内容
                for child in answer_section.children:
                    if hasattr(child, 'name'):
                        if child.name == 'p':
                            p_tag = soup.new_tag('p')
                            if child.find('a'):
                                self._copy_paragraph_with_links(child, p_tag, soup)
                            else:
                                p_tag.string = child.get_text(strip=True)
                            answer_div.append(p_tag)
                        elif child.name == 'ol':
                            ol_tag = soup.new_tag('ol')
                            for li in child.find_all('li'):
                                li_tag = soup.new_tag('li')
                                li_tag.string = li.get_text(strip=True)
                                ol_tag.append(li_tag)
                            answer_div.append(ol_tag)
                        elif child.name == 'table':
                            clean_table = self._clean_pricing_table(child, soup)
                            if clean_table:
                                answer_div.append(clean_table)
                
                faq_item_div.append(answer_div)
            
            if faq_item_div.children:
                content_elements.append(faq_item_div)
        
        return content_elements
    
    def _prepare_product_for_cms(self, soup: BeautifulSoup, product_name: str) -> BeautifulSoup:
        """为CMS做最后的准备"""
        
        print("✨ 第二步：CMS优化...")
        
        # 移除空的容器
        self._remove_empty_containers(soup)
        
        # 确保表格有适当的样式类
        for table in soup.find_all('table'):
            if 'pricing-table' not in table.get('class', []):
                table['class'] = 'pricing-table'
        
        # 添加产品信息标识
        product_info = soup.new_tag('div', **{'class': 'product-info'})
        product_info.string = f"产品: {product_name}"
        
        # 将产品信息插入到最前面
        if soup.contents:
            soup.insert(0, product_info)
        else:
            soup.append(product_info)
        
        print("  ✓ CMS优化完成")
        
        return soup
    
    def _remove_empty_containers(self, soup: BeautifulSoup):
        """移除空的容器"""
        
        for _ in range(3):
            empty_elements = []
            
            for element in soup.find_all(['div', 'section', 'article', 'span']):
                if not element.get_text(strip=True) and not element.find_all(['img', 'input', 'button', 'table']):
                    empty_elements.append(element)
            
            for element in empty_elements:
                element.decompose()
                
            if not empty_elements:
                break
    
    def _build_final_product_html(self, soup: BeautifulSoup, product_name: str) -> str:
        """构建最终的HTML输出"""
        
        print("🏗️ 第三步：构建最终HTML...")
        
        # 构建完整的HTML文档
        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{product_name} - Azure云计算</title>
    <meta name="description" content="{product_name}的详细信息和定价">
    <style>
        /* CMS友好的基础样式 */
        .product-banner {{
            margin-bottom: 2rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-left: 4px solid #0078d4;
        }}
        
        .product-title {{
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #0078d4;
        }}
        
        .product-description {{
            color: #666;
            line-height: 1.5;
        }}
        
        .region-info {{
            background-color: #e7f3ff;
            padding: 0.5rem 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
            color: #0078d4;
        }}
        
        .pricing-content {{
            margin-bottom: 2rem;
        }}
        
        .table-title {{
            font-size: 1.2rem;
            margin: 1.5rem 0 0.5rem 0;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 0.5rem;
        }}
        
        .pricing-table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
            background-color: white;
        }}
        
        .pricing-table th,
        .pricing-table td {{
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }}
        
        .pricing-table th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }}
        
        .pricing-table tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .faq-section {{
            margin-top: 2rem;
        }}
        
        .faq-title {{
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #0078d4;
            border-bottom: 2px solid #0078d4;
            padding-bottom: 0.5rem;
        }}
        
        .faq-item {{
            margin-bottom: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }}
        
        .faq-question {{
            background-color: #f8f9fa;
            padding: 0.75rem;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
        }}
        
        .faq-answer {{
            padding: 0.75rem;
            line-height: 1.5;
            color: #666;
        }}
        
        .sla-section {{
            margin-top: 2rem;
            padding: 1rem;
            background-color: #f0f8ff;
            border-radius: 4px;
        }}
        
        .sla-title {{
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #0078d4;
        }}
        
        .sla-content {{
            margin-bottom: 0.5rem;
            color: #333;
            line-height: 1.5;
        }}
    </style>
</head>
<body>
{str(soup)}
</body>
</html>"""
        
        print("  ✓ HTML文档构建完成")
        
        return html_template
    
    def _verify_product_extraction_result(self, html_content: str, product_name: str) -> Dict[str, any]:
        """验证提取结果"""
        
        verification_soup = BeautifulSoup(html_content, 'html.parser')
        
        # 统计各种内容组件
        components = {
            "product_banner": len(verification_soup.find_all('section', class_='product-banner')),
            "product_intro": len(verification_soup.find_all('section', class_='product-intro')),
            "pricing_content": len(verification_soup.find_all('section', class_='pricing-content')),
            "pricing_tables": len(verification_soup.find_all('table', class_='pricing-table')),
            "table_titles": len(verification_soup.find_all(['h3'], class_='table-title')),
            "pricing_notes": len(verification_soup.find_all('p', class_='pricing-note')),
            "faq_items": len(verification_soup.find_all('div', class_='faq-item')),
            "support_info": len(verification_soup.find_all('section', class_=['support-info', 'sla-section'])),
            "headings": len(verification_soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])),
            "paragraphs": len(verification_soup.find_all('p')),
            "links": len(verification_soup.find_all('a'))
        }
        
        # 检查关键内容是否存在
        has_key_content = {
            "product_banner": components["product_banner"] > 0,
            "product_intro": components["product_intro"] > 0,
            "detailed_pricing": components["pricing_content"] > 0 and components["pricing_tables"] >= 3,
            "pricing_tables": components["pricing_tables"] >= 3,  # Service Bus应该有多个定价表格
            "table_structure": components["table_titles"] >= 3,    # 应该有多个表格标题
            "faq_section": components["faq_items"] >= 5,          # Service Bus有14个FAQ
            "support_section": components["support_info"] > 0
        }
        
        verification = {
            "has_product_info": bool(verification_soup.find('div', class_='product-info')),
            "has_complete_structure": all(has_key_content.values()),
            "content_components": components,
            "key_content_check": has_key_content,
            "text_length": len(verification_soup.get_text(strip=True)),
            "html_size": len(html_content),
            "is_valid_html": html_content.strip().startswith('<!DOCTYPE html>'),
            "product_name": product_name
        }
        
        # 内容完整性检查
        verification["content_completeness"] = {
            "has_substantial_text": verification["text_length"] > 3000,  # Service Bus内容应该很丰富
            "has_rich_pricing": (
                components["pricing_tables"] >= 5 and          # 至少5个定价表格
                components["table_titles"] >= 4 and           # 至少4个表格标题
                components["pricing_content"] > 0              # 有定价内容部分
            ),
            "has_comprehensive_faq": components["faq_items"] >= 10,  # 丰富的FAQ
            "has_navigation_structure": components["headings"] >= 8,
            "has_interactive_content": components["links"] > 0,
            "has_detailed_notes": components["pricing_notes"] > 0
        }
        
        return verification
    
    def debug_pricing_extraction(self, html_file_path: str) -> Dict[str, any]:
        """调试定价内容提取过程"""
        
        print(f"\n🔧 调试模式：分析定价内容提取过程")
        print(f"📁 源文件: {html_file_path}")
        print("=" * 70)
        
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        debug_info = {
            "containers_found": [],
            "tab_structures": [],
            "pricing_elements": [],
            "raw_structure": {}
        }
        
        # 0. 检查原始HTML中是否存在目标结构
        print("🔍 第零步：检查原始HTML结构")
        
        # 直接在原始HTML中搜索关键字符串
        has_technical_azure = 'technical-azure-selector' in html_content
        has_tab_control = 'tab-control-container' in html_content
        has_tab_active = 'tab-active' in html_content
        
        print(f"  📋 原始HTML包含:")
        print(f"      technical-azure-selector: {'✅' if has_technical_azure else '❌'}")
        print(f"      tab-control-container: {'✅' if has_tab_control else '❌'}")
        print(f"      tab-active: {'✅' if has_tab_active else '❌'}")
        
        debug_info["raw_structure"] = {
            "has_technical_azure": has_technical_azure,
            "has_tab_control": has_tab_control,
            "has_tab_active": has_tab_active
        }
        
        # 1. 使用更宽泛的选择器检查所有div
        print("\n🔍 第一步：检查所有包含关键词的div")
        
        all_divs = soup.find_all('div')
        relevant_divs = []
        
        for div in all_divs:
            classes = div.get('class', [])
            class_str = ' '.join(classes) if classes else ''
            
            # 检查是否包含关键词
            if any(keyword in class_str for keyword in ['technical', 'azure', 'selector', 'tab-control', 'tab-active']):
                relevant_divs.append({
                    "classes": class_str,
                    "children_count": len(list(div.children)),
                    "tables_count": len(div.find_all('table')),
                    "headings_count": len(div.find_all(['h1', 'h2', 'h3', 'h4'])),
                    "text_preview": div.get_text(strip=True)[:50]
                })
        
        print(f"  📦 找到相关div: {len(relevant_divs)} 个")
        for i, div_info in enumerate(relevant_divs):
            print(f"      Div {i}: class='{div_info['classes']}'")
            print(f"          表格:{div_info['tables_count']}, 标题:{div_info['headings_count']}")
            print(f"          预览: {div_info['text_preview']}...")
        
        debug_info["containers_found"] = relevant_divs
        
        # 2. 精确匹配目标容器
        print("\n🔍 第二步：精确匹配目标容器")
        
        # 使用多种选择器尝试
        selectors_to_try = [
            ('technical-azure-selector', 'div[class*="technical-azure-selector"]'),
            ('tab-control-container', 'div[class*="tab-control-container"]'),
            ('tab-control-selector', 'div[class*="tab-control-selector"]'),
            ('tab-active', 'div[class*="tab-active"]')
        ]
        
        for name, selector in selectors_to_try:
            elements = soup.select(selector)
            print(f"  🎯 {name}: 找到 {len(elements)} 个元素")
            
            for i, elem in enumerate(elements):
                classes = ' '.join(elem.get('class', []))
                tables = len(elem.find_all('table'))
                headings = len(elem.find_all(['h2', 'h3', 'h4']))
                print(f"      元素 {i}: class='{classes}', 表格:{tables}, 标题:{headings}")
        
        # 3. 检查具体的定价元素
        print("\n🔍 第三步：检查定价相关元素")
        
        all_tables = soup.find_all('table')
        print(f"  📊 总表格数: {len(all_tables)}")
        
        h2_headings = soup.find_all('h2')
        pricing_related_h2 = []
        for h2 in h2_headings:
            text = h2.get_text(strip=True)
            if any(keyword in text for keyword in ['定价', '价格', '费用', '操作', '连接', '中继', '混合']):
                pricing_related_h2.append(text)
        
        print(f"  🏷️ 定价相关标题: {len(pricing_related_h2)}")
        for title in pricing_related_h2:
            print(f"      - {title}")
        
        debug_info["pricing_elements"] = {
            "total_tables": len(all_tables),
            "pricing_headings": pricing_related_h2
        }
        
        # 4. 检查表格和标题的父容器
        print("\n🔍 第四步：分析表格和标题的父容器")
        
        for i, heading in enumerate(pricing_related_h2):
            h2_elements = [h for h in h2_headings if h.get_text(strip=True) == heading]
            if h2_elements:
                h2_elem = h2_elements[0]
                
                # 向上查找父容器
                parent_chain = []
                current = h2_elem.parent
                while current and len(parent_chain) < 5:
                    if current.name == 'div' and current.get('class'):
                        parent_chain.append(' '.join(current.get('class')))
                    current = current.parent
                
                print(f"  📋 标题 '{heading}' 的父容器链:")
                for j, parent_class in enumerate(parent_chain):
                    print(f"      级别 {j+1}: {parent_class}")
        
        # 5. 模拟实际提取过程
        print("\n🔍 第五步：模拟实际提取过程")
        
        # 查找pure-content容器
        pure_content = soup.find('div', class_='pure-content')
        if pure_content:
            print("  ✅ 找到 pure-content 容器")
            
            # 使用CSS选择器精确查找
            tech_containers = pure_content.select('div[class*="technical-azure-selector"]')
            tab_containers = pure_content.select('div[class*="tab-control-container"]')
            selector_containers = pure_content.select('div[class*="tab-control-selector"]')
            
            print(f"  🎯 CSS选择器查找结果:")
            print(f"      technical-azure-selector: {len(tech_containers)} 个")
            print(f"      tab-control-container: {len(tab_containers)} 个")
            print(f"      tab-control-selector: {len(selector_containers)} 个")
            
            for i, container in enumerate(tech_containers):
                classes = ' '.join(container.get('class', []))
                tables = len(container.find_all('table'))
                print(f"        技术容器 {i}: {classes}, 表格数:{tables}")
            
            for i, container in enumerate(tab_containers):
                classes = ' '.join(container.get('class', []))
                tables = len(container.find_all('table'))
                print(f"        Tab容器 {i}: {classes}, 表格数:{tables}")
            
            # 如果还是找不到，尝试直接搜索包含表格的div
            divs_with_tables = []
            for div in pure_content.find_all('div'):
                tables_in_div = div.find_all('table')
                classes = ' '.join(div.get('class', []))
                if len(tables_in_div) >= 2:  # 至少包含2个表格
                    divs_with_tables.append({
                        "classes": classes,
                        "tables_count": len(tables_in_div),
                        "element": div
                    })
            
            print(f"  📊 包含多个表格的div: {len(divs_with_tables)} 个")
            for div_info in divs_with_tables:
                print(f"      class='{div_info['classes']}', 表格数:{div_info['tables_count']}")
                
                # 检查这些div是否包含我们想要的内容
                div_element = div_info['element']
                h2_in_div = [h2.get_text(strip=True) for h2 in div_element.find_all('h2')]
                if h2_in_div:
                    print(f"          包含标题: {h2_in_div}")
        else:
            print("  ❌ 未找到 pure-content 容器")
        
        return debug_info
    
    def save_product_cms_html(self, result: Dict[str, any], custom_filename: str = "") -> str:
        """保存产品CMS HTML文件"""
        
        if not result["success"]:
            print(f"❌ 无法保存失败的提取结果")
            return ""
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        product_name = result.get("product_name", "unknown_product")
        
        # 清理产品名称用于文件名
        safe_product_name = re.sub(r'[^\w\-_]', '_', product_name)
        
        if custom_filename:
            filename = custom_filename
        else:
            filename = f"{safe_product_name}_cms_{timestamp}.html"
        
        file_path = self.output_dir / filename
        
        # 保存HTML文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(result["html_content"])
        
        print(f"\n💾 产品CMS HTML已保存: {file_path}")
        print(f"📄 文件大小: {result['statistics']['final_size']:,} 字节")
        print(f"📊 压缩比: {result['statistics']['compression_ratio']*100:.1f}%")
        
        # 保存统计信息
        stats_path = file_path.with_suffix('.stats.json')
        stats_data = {
            "product_name": result["product_name"],
            "statistics": result["statistics"],
            "verification": result["verification"],
            "extraction_info": result["extraction_info"]
        }
        
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats_data, f, ensure_ascii=False, indent=2)
        
        print(f"📋 统计信息: {stats_path}")
        
        return str(file_path)


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="通用产品页面CMS HTML提取器")
    parser.add_argument("html_file", help="产品HTML源文件路径")
    parser.add_argument("-p", "--product", help="产品名称（可选，会自动检测）")
    parser.add_argument("-o", "--output", default="cms_output", help="输出目录")
    parser.add_argument("--filename", help="指定输出文件名")
    parser.add_argument("--debug", action="store_true", help="启用调试模式，分析定价内容提取过程")
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not os.path.exists(args.html_file):
        print(f"❌ HTML文件不存在: {args.html_file}")
        return 1
    
    try:
        # 创建提取器
        extractor = GenericProductCMSExtractor(args.output)
        
        # 显示版本信息
        print("🚀 通用产品页面CMS HTML提取器")
        print("📄 专门生成适合CMS导入的干净HTML文件")
        print("🎯 适用于无区域筛选的Azure产品页面")
        
        # 调试模式
        if args.debug:
            print("\n🔧 启用调试模式")
            debug_info = extractor.debug_pricing_extraction(args.html_file)
            
            print(f"\n📋 调试摘要:")
            print(f"  📦 找到容器: {len(debug_info['containers_found'])} 个")
            print(f"  🎛️ Tab结构: {len(debug_info['tab_structures'])} 个")
            print(f"  📊 总表格数: {debug_info['pricing_elements']['total_tables']}")
            print(f"  🏷️ 定价标题: {len(debug_info['pricing_elements']['pricing_headings'])} 个")
            
            print(f"\n💡 如果定价内容提取失败，请检查以上信息")
            return 0
        
        # 正常提取模式
        # 提取产品页面
        result = extractor.extract_product_cms_html(args.html_file, args.product or "")
        
        if result["success"]:
            output_file = extractor.save_product_cms_html(result, args.filename)
            print(f"✅ 产品页面CMS HTML提取完成: {output_file}")
            
            # 显示详细的内容摘要
            verification = result["verification"]
            components = verification["content_components"]
            
            print(f"\n📊 详细内容摘要:")
            print(f"  🏷️ 产品名称: {result['product_name']}")
            print(f"  🏠 产品横幅: {components['product_banner']} 个")
            print(f"  📝 产品介绍: {components['product_intro']} 部分")
            print(f"  💰 定价内容: {components['pricing_content']} 部分")
            print(f"  📋 定价表格: {components['pricing_tables']} 个")
            print(f"  🏷️ 表格标题: {components['table_titles']} 个")
            print(f"  📌 定价备注: {components['pricing_notes']} 个")
            print(f"  ❓ FAQ项目: {components['faq_items']} 个")
            print(f"  🛠️ 支持信息: {components['support_info']} 部分")
            print(f"  📝 总标题数: {components['headings']} 个")
            print(f"  📄 总段落数: {components['paragraphs']} 个")
            print(f"  🔗 链接数量: {components['links']} 个")
            
            # 显示内容完整性检查
            completeness = verification["content_completeness"]
            print(f"\n🔍 内容完整性检查:")
            print(f"  📏 文本长度充足: {'✅' if completeness['has_substantial_text'] else '❌'}")
            print(f"  💰 定价信息丰富: {'✅' if completeness['has_rich_pricing'] else '❌'}")
            print(f"  ❓ FAQ内容全面: {'✅' if completeness['has_comprehensive_faq'] else '❌'}")
            print(f"  🧭 导航结构完善: {'✅' if completeness['has_navigation_structure'] else '❌'}")
            print(f"  🔗 包含交互内容: {'✅' if completeness['has_interactive_content'] else '❌'}")
            print(f"  📝 详细备注说明: {'✅' if completeness['has_detailed_notes'] else '❌'}")
            
            # 显示关键内容检查
            key_check = verification["key_content_check"]
            print(f"\n🎯 关键内容检查:")
            print(f"  🏠 产品横幅: {'✅' if key_check['product_banner'] else '❌'}")
            print(f"  📝 产品介绍: {'✅' if key_check['product_intro'] else '❌'}")
            print(f"  💰 详细定价: {'✅' if key_check['detailed_pricing'] else '❌'}")
            print(f"  📋 定价表格: {'✅' if key_check['pricing_tables'] else '❌'} (需要>=3个)")
            print(f"  🏷️ 表格结构: {'✅' if key_check['table_structure'] else '❌'} (需要>=3个标题)")
            print(f"  ❓ FAQ部分: {'✅' if key_check['faq_section'] else '❌'} (需要>=5个)")
            print(f"  🛠️ 支持部分: {'✅' if key_check['support_section'] else '❌'}")
            
            # 特别检查定价内容
            if components['pricing_content'] == 0:
                print(f"\n⚠️  警告: 未检测到定价内容部分")
                print(f"    建议使用 --debug 选项分析原因")
                print(f"    python {__file__} {args.html_file} --debug")
            elif components['pricing_tables'] < 3:
                print(f"\n⚠️  警告: 定价表格数量较少 ({components['pricing_tables']} < 3)")
                print(f"    Service Bus页面应该包含多个定价表格")
            
            overall_quality = "优秀" if verification["has_complete_structure"] else "良好"
            print(f"\n🏆 总体质量评估: {overall_quality}")
        else:
            print(f"❌ 提取失败: {result.get('error', '未知错误')}")
            return 1
        
        print("\n🎉 产品页面提取任务完成！")
        print("📄 生成的HTML文件可直接导入CMS系统")
        return 0
        
    except Exception as e:
        print(f"❌ 提取过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())