
<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, MySQL Database on Azure, 价格详情, 定价, 计费" name="keywords"/>
  <meta content="了解Azure Database for PostgreSQL的价格详情。Azure Database for PostgreSQL提供六个不同的版本，性能从低到高按倍数提高。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   Azure Database for PostgreSQL定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/postgresql/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute;
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <style type="text/css">
        .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;
            }
            .pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
       </style>
       <tags ms.date="09/30/2015" ms.service="postgresql" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/service-banner-postgressql.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           Azure
           <small>
            Database for PostgreSQL
           </small>
          </h2>
          <h4>
           面向应用开发人员的托管 PostgreSQL 数据库服务
          </h4>
         </div>
        </div>
       </div>

       <div class="pricing-page-section">
        <p>
         Azure Database for PostgreSQL 通过内置功能（包括高可用性）提供用于应用开发和部署的完全托管数据库服务，无需额外付费。
        </p>
       </div>

       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure Database for PostgreSQL
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent0" href="javascript:void(0)" id="Azure Database for PostgreSQL">
                Azure Database for PostgreSQL
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent0" selected="selected" value="Azure Database for PostgreSQL">
              Azure Database for PostgreSQL
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 3
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
                <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                 中国东部 3
                </a>
               </li>
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li>
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china3" selected="selected" value="east-china3">
              中国东部 3
             </option>
             <option data-href="#north-china3" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <div class="tab-content">
         <div class="tabpanel" id="tabContent0">
          <div class="technical-azure-selector pricing-detail-tab">

           <ul class="tab-nav">
<!--             <li class="active">
             <a data-href="#tabContent5" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-basic">
              单个服务器
             </a>
            </li> -->
            <li class="active">
             <a data-href="#tabContent6" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-general">
              灵活服务器
             </a>
            </li>
           </ul>

           <div class="tab-content">
            <div class="tab-panel" id="tabContent5">
             <p>
              单一服务器是预配置的数据库服务器，最适合于数据库自定义要求最低的工作负载，包括：
             </p>
             <ul>
              <li>
               自动数据库修补和维护
              </li>
              <li>
               具有 99.99% SLA 的内置高可用性
              </li>
             </ul>
             <a href="https://docs.azure.cn/zh-cn/postgresql/overview" style="float: none;background-color: white;display: inline;color: #006FC3;padding-left: 0px;margin-left: 0px; padding-bottom: 10px;">
              详细了解 Azure Database for PostgreSQL 单一服务器
             </a>
             <!-- BEGIN: TAB-CONTROL -->
             <div class="technical-azure-selector pricing-detail-tab">
              <ul class="tab-nav">
               <li class="active">
                <a data-href="#tabContent1" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-basic">
                 基本
                </a>
               </li>
               <li>
                <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-general">
                 常规用途
                </a>
               </li>
               <li>
                <a data-href="#tabContent3" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-memory">
                 内存优化
                </a>
               </li>
              </ul>
              <!-- BEGIN: TAB-CONTAINER-1 -->
              <div class="tab-content">
               <!-- BEGIN: Level 1 tab content panel 1 -->
               <div class="tab-panel" id="tabContent1">
                <!-- -->
                <h2>
                 基本
                </h2>
                <p>
                 需要轻型计算和 I/O 性能的工作负荷。
                </p>
                <h3>
                 计算
                </h3>
                <p>
                 在虚拟核心 (vCore) 中预配计算。vCore 表示通过在计算生成间进行选择的选项提供的逻辑 CPU。
                </p>
                <!-- <h4>Gen 4（第四代计算）</h4>
                                <p>第四代逻辑 CPU 基于 Intel E5-2673 v3 (Haswell) 2.4 GHz 处理器。</p>
                                <table cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <th align="left"><strong>vCore</strong></th>
                                        <th align="left"><strong>价格</strong></th>
                                    </tr>
                                    <tr>
                                        <td>1</td>
                                        <td>¥0.2699/小时<br/>（~¥200.8056/月）</td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>¥0.5398/小时<br/>（~¥401.6112/月）</td>
                                    </tr>
                                </table> -->
                <h4>
                 Gen 5（第五代计算）
                </h4>
                <p>
                 第五代逻辑 CPU 基于 Intel E5-2673 v4 (Broadwell) 2.3 GHz 处理器。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                    vCore
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   1
                  </td>
                  <td>
                   ¥0.2699/小时
                   <br/>
                   （~¥200.8056/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   2
                  </td>
                  <td>
                   ¥0.5398/小时
                   <br/>
                   （~¥401.6112/月）
                  </td>
                 </tr>
                </table>
                <h3>
                 存储
                </h3>
                <p>
                 需要为对服务器预配的存储空间付费。最多可预配 1 TB 的存储空间。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   GB/月
                  </td>
                  <td>
                   ¥0.7938
                  </td>
                 </tr>
                </table>
                <h3>
                 备份
                </h3>
                <p>
                 备份存储是与服务器的自动备份关联的存储。 增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   本地冗余 GB/月
                  </td>
                  <td>
                   ¥0.7938
                  </td>
                 </tr>
                </table>
                <h3>
                 I/O
                </h3>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   GB/月
                  </td>
                  <td>
                   ¥0.7938
                  </td>
                 </tr>
                </table>
                <!-- <p>在 2018 年 9 月 1 日之前，不会收取 I/O 费用。</p> -->
               </div>
               <!-- END: TAB-CONTAINER-1 -->
               <!-- BEGIN: TAB-CONTAINER-2 -->
               <div class="tab-panel" id="tabContent2">
                <!-- -->
                <h2>
                 常规用途
                </h2>
                <p>
                 大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放 I/O 吞吐量。
                </p>
                <h3>
                 计算
                </h3>
                <p>
                 在虚拟核心 (vCore) 中预配计算。vCore 表示通过在计算生成间进行选择的选项提供的逻辑 CPU。
                </p>
                <!-- <h4>Gen 4（第四代计算）</h4>
                                <p>第四代逻辑 CPU 基于 Intel E5-2673 v3 (Haswell) 2.4 GHz 处理器。</p>
                                <table cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <th align="left"><strong>vCore</strong></th>
                                        <th align="left"><strong>价格</strong></th>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>¥0.6676/小时<br/>（~¥496.6944/月）</td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>¥1.3352/小时<br/>（~¥993.3888/月）</td>
                                    </tr>
                                    <tr>
                                        <td>8</td>
                                        <td>¥2.6704/小时<br/>（~¥1,986.7776/月）</td>
                                    </tr>
                                    <tr>
                                        <td>16</td>
                                        <td>¥5.3408/小时<br/>（~¥3,973.5552/月）</td>
                                    </tr>
                                    <tr>
                                        <td>32</td>
                                        <td>¥10.6816/小时<br/>（~¥7,947.1104/月）</td>
                                    </tr>
                                </table> -->
                <h4>
                 Gen 5（第五代计算）
                </h4>
                <p>
                 第五代逻辑 CPU 基于 Intel E5-2673 v4 (Broadwell) 2.3 GHz 处理器。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                    vCore
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    内存
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   2
                  </td>
                  <td>
                   10 GB
                  </td>
                  <td>
                   ¥0.6676/小时
                   <br/>
                   （~¥496.6944/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   4
                  </td>
                  <td>
                   20 GB
                  </td>
                  <td>
                   ¥1.3352/小时
                   <br/>
                   （~¥993.3888/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   8
                  </td>
                  <td>
                   40 GB
                  </td>
                  <td>
                   ¥2.6704/小时
                   <br/>
                   （~¥1,986.7776/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   16
                  </td>
                  <td>
                   80 GB
                  </td>
                  <td>
                   ¥5.3408/小时
                   <br/>
                   （~¥3,973.5552/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   32
                  </td>
                  <td>
                   160 GB
                  </td>
                  <td>
                   ¥10.6816/小时
                   <br/>
                   （~¥7,947.1104/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   64
                  </td>
                  <td>
                   320 GB
                  </td>
                  <td>
                   ¥21.359/小时
                   <br/>
                   （~¥15,891.096‬/月）
                  </td>
                 </tr>
                </table>
                <h3>
                 存储
                </h3>
                <p>
                 需要为对服务器预配的存储空间付费。最多可预配 2 TB 的存储空间。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   GB/月
                  </td>
                  <td>
                   ¥0.8033
                  </td>
                 </tr>
                </table>
                <h3>
                 备份
                </h3>
                <p>
                 备份存储是与服务器的自动备份关联的存储。 增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   本地冗余 GB/月
                  </td>
                  <td>
                   ¥0.7938
                  </td>
                 </tr>
                 <tr>
                  <td>
                   异地冗余 GB/月
                  </td>
                  <td>
                   ¥1.5875
                  </td>
                 </tr>
                </table>
                <p>
                 限时促销：在 2018 年 8 月 1 日之前，不会收取备份存储费用。
                </p>
               </div>
               <!-- END: TAB-CONTAINER-2 -->
               <!-- BEGIN: TAB-CONTAINER-3 -->
               <div class="tab-panel" id="tabContent3">
                <!-- -->
                <h2>
                 内存优化
                </h2>
                <p>
                 高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。
                </p>
                <h3>
                 计算
                </h3>
                <p>
                 在虚拟核心 (vCore) 中预配计算。vCore 表示通过在计算生成间进行选择的选项提供的逻辑 CPU。
                </p>
                <!-- <h4>Gen 4（第四代计算）</h4>
                                <p>第四代逻辑 CPU 基于 Intel E5-2673 v3 (Haswell) 2.4 GHz 处理器。</p>
                                <table cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <th align="left"><strong>vCore</strong></th>
                                        <th align="left"><strong>价格</strong></th>
                                    </tr>
                                    <tr>
                                    <td>2</td>
                                    <td>¥1.8748/小时<br/>（~¥1394.8512/月）</td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>¥3.7496/小时<br/>（~¥2789.7024/月）</td>
                                    </tr>
                                    <tr>
                                        <td>8</td>
                                        <td>¥7.4992/小时<br/>（~¥5579.4048/月）</td>
                                    </tr>
                                    <tr>
                                        <td>16</td>
                                        <td>¥14.9984/小时<br/>（~¥11158.8096/月）</td>
                                    </tr>
                                </table> -->
                <h4>
                 Gen 5（第五代计算）
                </h4>
                <p>
                 第五代逻辑 CPU 基于 Intel E5-2673 v4 (Broadwell) 2.3 GHz 处理器。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                    vCore
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   2
                  </td>
                  <td>
                   ¥1.8748/小时
                   <br/>
                   （~¥1394.8512/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   4
                  </td>
                  <td>
                   ¥3.7496/小时
                   <br/>
                   （~¥2789.7024/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   8
                  </td>
                  <td>
                   ¥7.4992/小时
                   <br/>
                   （~¥5579.4048/月）
                  </td>
                 </tr>
                 <tr>
                  <td>
                   16
                  </td>
                  <td>
                   ¥14.9984/小时
                   <br/>
                   （~¥11158.8096/月）
                  </td>
                 </tr>
                </table>
                <h3>
                 存储
                </h3>
                <p>
                 需要为对服务器预配的存储空间付费。最多可预配 2 TB 的存储空间。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   GB/月
                  </td>
                  <td>
                   ¥0.8033
                  </td>
                 </tr>
                </table>
                <h3>
                 备份
                </h3>
                <p>
                 备份存储是与服务器的自动备份关联的存储。 增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
                </p>
                <table cellpadding="0" cellspacing="0" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   本地冗余 GB/月
                  </td>
                  <td>
                   ¥0.7938
                  </td>
                 </tr>
                 <tr>
                  <td>
                   异地冗余 GB/月
                  </td>
                  <td>
                   ¥1.5875
                  </td>
                 </tr>
                </table>
                <p>
                 限时促销：在 2018 年 8 月 1 日之前，不会收取备份存储费用。
                </p>
               </div>
               <!-- END: TAB-CONTAINER-3 -->
              </div>
             </div>
            </div>
            <div class="tab-panel" id="tabContent6">
            <!-- 删除灵活服务器tab下的内容 -->
             <!-- <p>
              灵活服务器通过简化的开发人员体验为数据库提供最大程度的控制，并且最适合具有以下要求的工作负载：
             </p>
             <ul>
              <li>
               用于数据库优化的自定义维护时段和其他配置参数
              </li>
              <li>
               相同 - 区域冗余高可用性
              </li>
              <li>
               停止/启动功能和可突发 SKU 以实现成本优化
              </li>
             </ul>
             <a href="https://docs.azure.cn/zh-cn/postgresql/flexible-server/overview" style="float: none;background-color: white;display: inline;color: #006FC3;padding-left: 0px;margin-left: 0px; padding-bottom: 10px;">
              详细了解 Azure Database for PostgreSQL 灵活服务器
             </a> -->
             <div class="technical-azure-selector pricing-detail-tab">
              <ul class="tab-nav">
               <li class="active">
                <a data-href="#tabContent7" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-basic">
                 可突发
                </a>
               </li>
               <li>
                <a data-href="#tabContent8" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-general">
                 常规用途
                </a>
               </li>
               <li>
                <a data-href="#tabContent9" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-memory">
                 内存优化
                </a>
               </li>
              </ul>
              <div class="tab-content">
               <div class="tab-panel" id="tabContent7">
                <h3>
                 可突发
                </h3>
                <p>
                 具有灵活计算要求的工作负载。
                </p>
                <div class="scroll-table" style="display: block;">
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-burstable-burstable" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     VCORES
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     即用即付
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    B1ms
                   </td>
                   <td>
                    1
                   </td>
                   <td>
                    2 GiB
                   </td>
                   <td>
                    ¥ 0.1/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    B2S
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    4 GiB
                   </td>
                   <td>
                    ￥0.35/小时
                   </td>
                  </tr>
                 </table>
                </div>
                <div class="scroll-table" style="display: block;">
                 <h3>
                  存储
                 </h3>
                 <p>
                  需要为你为服务器配置的存储付费。存储最多可预配为 16 TiB
                 </p>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-burstable-storage" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    GiB/月
                   </td>
                   <td>
                    ¥ 1.17024
                   </td>
                  </tr>
                 </table>
                </div>
                <!--
                                    <h3>Storage - Additional IOPS</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <th align="left"><strong></strong></th>
                                        <th align="left"><strong>价格</strong></th>
                                    </tr>
                                    <tr>
                                        <td>GB/月</td>
                                        <td>¥ 0.3528</td>
                                    </tr>
                                </table>
                            -->
                <div class="scroll-table" style="display: block;">
                 <h3>
                  备份存储
                 </h3>
                 <p>
                  备份存储是与服务器自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GiB/月收费。
                 </p>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-burstable-backstorage" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     注意
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    GiB/月
                   </td>
                   <td>
                    ¥ 0.96672
                   </td>
                   <td>
                    GRS 需要两倍的存储容量，因为它会创建副本。如果已将备份存储配置为异地冗余(GRS)，则价格将是此价格的 2 倍。每 GiB 价格保持不变。
                   </td>
                  </tr>
                 </table>
                </div>
               </div>
               <!-- END: TAB-CONTAINER-1 -->
               <!-- BEGIN: TAB-CONTAINER-2 -->
               <div class="tab-panel" id="tabContent8">
                <!-- -->
                <h3>
                  常规用途
                 </h3>
                 <p>
                  大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放 I/O 吞吐量。
                 </p>
                <div class="scroll-table" style="display: block;">
                    <h3>
                     Dsv3 系列
                    </h3>
                    <table cellpadding="0" cellspacing="0" id="postgresql-flexible-general-dsv3" width="100%">
                     <tr>
                      <th align="left">
                       <strong>
                        实例
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        vCores
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        内存
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        即用即付
                       </strong>
                      </th>
                     </tr>
                     <tr>
                      <td>
                       D2s v3
                      </td>
                      <td>
                       2
                      </td>
                      <td>
                       8 GiB
                      </td>
                      <td>
                       ¥ 1.03/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D4s v3
                      </td>
                      <td>
                       4
                      </td>
                      <td>
                       16 GiB
                      </td>
                      <td>
                       ¥ 2.05/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D8s v3
                      </td>
                      <td>
                       8
                      </td>
                      <td>
                       32 GiB
                      </td>
                      <td>
                       ¥ 4.1/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D16s v3
                      </td>
                      <td>
                       16
                      </td>
                      <td>
                       64 GiB
                      </td>
                      <td>
                       ¥ 8.2/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D32s v3
                      </td>
                      <td>
                       32
                      </td>
                      <td>
                       128 GiB
                      </td>
                      <td>
                       ¥ 16.41/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D48s v3
                      </td>
                      <td>
                       48
                      </td>
                      <td>
                       192 GiB
                      </td>
                      <td>
                       ¥ 24.62/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D64s v3
                      </td>
                      <td>
                       64
                      </td>
                      <td>
                       256 GiB
                      </td>
                      <td>
                       ¥ 32.83/小时
                      </td>
                     </tr>
                    </table>            
                </div>
                 <div class="scroll-table" style="display: block;">
                    <h3>
                     Ddsv4 系列
                    </h3>
                    <table cellpadding="0" cellspacing="0" id="postgresql-flexible-general-ddsv4" width="100%">
                     <tr>
                      <th align="left">
                       <strong>
                        实例
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        vCores
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        内存
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        即用即付
                       </strong>
                      </th>
                     </tr>
                     <tr>
                      <td>
                       D2ds v4
                      </td>
                      <td>
                       2
                      </td>
                      <td>
                       8 GiB
                      </td>
                      <td>
                       ¥ 1.8/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D4ds v4
                      </td>
                      <td>
                       4
                      </td>
                      <td>
                       16 GiB
                      </td>
                      <td>
                       ¥ 3.6/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D8ds v4
                      </td>
                      <td>
                       8
                      </td>
                      <td>
                       32 GiB
                      </td>
                      <td>
                       ¥ 7.2/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D16ds v4
                      </td>
                      <td>
                       16
                      </td>
                      <td>
                       64 GiB
                      </td>
                      <td>
                       ¥ 14.4/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D32ds v4
                      </td>
                      <td>
                       32
                      </td>
                      <td>
                       128 GiB
                      </td>
                      <td>
                       ¥ 28.8/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D48ds v4
                      </td>
                      <td>
                       48
                      </td>
                      <td>
                       192 GiB
                      </td>
                      <td>
                       ¥ 43.2/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                       D64ds v4
                      </td>
                      <td>
                       64
                      </td>
                      <td>
                       256 GiB
                      </td>
                      <td>
                       ¥ 57.6/小时
                      </td>
                     </tr>
                    </table>                
                 </div>

                 <!-- Ddsv5 系列 -->
                 <div class="scroll-table" style="display: block;">
                    <h3>Ddsv5 系列</h3>
                    <table cellpadding="0" cellspacing="0" id="postgresql-flexible-general-ddsv5" width="100%">
                     <tr>
                      <th align="left">
                       <strong>
                        实例
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        vCore 数
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        内存
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        即用即付
                       </strong>
                      </th>
                     </tr>
                     <tr>
                      <td>
                        D2ds v5
                      </td>
                      <td>
                        2
                      </td>
                      <td>
                        8 GB
                      </td>
                      <td>
                        ￥1.78/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                        D4ds v5
                      </td>
                      <td>
                        4
                      </td>
                      <td>
                        16 GB
                      </td>
                      <td>
                        ￥3.56/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                        D8ds v5
                      </td>
                      <td>
                        8
                      </td>
                      <td>
                        32 GB
                      </td>
                      <td>
                        ￥7.12/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                        D16ds v5
                      </td>
                      <td>
                        16
                      </td>
                      <td>
                        64 GB
                      </td>
                      <td>
                        ￥14.25/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                        D32ds v5
                      </td>
                      <td>
                        32
                      </td>
                      <td>
                        128 GB
                      </td>
                      <td>
                        ￥28.49/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                        D48ds v5
                      </td>
                      <td>
                        48
                      </td>
                      <td>
                        192 GB
                      </td>
                      <td>
                        ￥42.74/小时
                      </td>
                     </tr>
                     <tr>
                      <td>
                        D64ds v5
                      </td>
                      <td>
                        64
                      </td>
                      <td>
                        256 GB
                      </td>
                      <td>
                        ￥56.99/小时
                      </td>
                     </tr>
                     <tr>
                        <td>
                            D96ds v5
                        </td>
                        <td>
                            96
                        </td>
                        <td>
                            384 GB
                        </td>
                        <td>
                            ￥85.48/小时
                        </td>
                       </tr>
                    </table>                
                 </div>

                 <!-- 中国东部3 - 灵活服务器 - 常规用途 - Ddsv5系列 -->
                 <div class="scroll-table" style="display: block;">
                  <h3>Ddsv5 系列</h3>
                  <table cellpadding="0" cellspacing="0" id="postgresql-flexible-general-ddsv5-east3" width="100%">
                   <tr>
                    <th align="left">
                     <strong>
                      实例 
                     </strong>
                    </th>
                    <th align="left">
                     <strong>
                      vCore 数
                     </strong>
                    </th>
                    <th align="left">
                     <strong>
                      内存
                     </strong>
                    </th>
                    <th align="left">
                     <strong>
                      即用即付
                     </strong>
                    </th>
                   </tr>
                   <tr>
                    <td>
                      D2ds v5
                    </td>
                    <td>
                      2
                    </td>
                    <td>
                      8 GB
                    </td>
                    <td>
                      ￥1.81/小时
                    </td>
                   </tr>
                   <tr>
                    <td>
                      D4ds v5
                    </td>
                    <td>
                      4
                    </td>
                    <td>
                      16 GB
                    </td>
                    <td>
                      ￥3.62/小时
                    </td>
                   </tr>
                   <tr>
                    <td>
                      D8ds v5
                    </td>
                    <td>
                      8
                    </td>
                    <td>
                      32 GB
                    </td>
                    <td>
                      ￥7.25/小时
                    </td>
                   </tr>
                   <tr>
                    <td>
                      D16ds v5
                    </td>
                    <td>
                      16
                    </td>
                    <td>
                      64 GB
                    </td>
                    <td>
                      ￥14.49/小时
                    </td>
                   </tr>
                   <tr>
                    <td>
                      D32ds v5
                    </td>
                    <td>
                      32
                    </td>
                    <td>
                      128 GB
                    </td>
                    <td>
                      ￥28.98/小时
                    </td>
                   </tr>
                   <tr>
                    <td>
                      D48ds v5
                    </td>
                    <td>
                      48
                    </td>
                    <td>
                      192 GB
                    </td>
                    <td>
                      ￥43.47/小时
                    </td>
                   </tr>
                   <tr>
                    <td>
                      D64ds v5
                    </td>
                    <td>
                      64
                    </td>
                    <td>
                      256 GB
                    </td>
                    <td>
                      ￥57.96/小时
                    </td>
                   </tr>
                   <tr>
                      <td>
                        D96ds v5
                      </td>
                      <td>
                          96
                      </td>
                      <td>
                          384 GB
                      </td>
                      <td>
                        ￥86.94/小时
                      </td>
                     </tr>
                  </table>                
               </div>
                
                <div class="scroll-table" style="display: block;">
                 <h3>
                  存储
                 </h3>
                 <p>
                  需要为你为服务器配置的存储付费。存储最多可预配为 16 TiB
                 </p>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-general-storage" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    GiB/月
                   </td>
                   <td>
                    ¥ 1.17024
                   </td>
                  </tr>
                 </table>
                </div>
                <div class="scroll-table" style="display: block;">
                 <h3>
                  备份存储
                 </h3>
                 <p>
                  备份存储是与服务器自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GiB/月收费。
                 </p>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-general-backstorage" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     注意
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    GiB/月
                   </td>
                   <td>
                    ¥ 0.96672
                   </td>
                   <td>
                    GRS 需要两倍的存储容量，因为它会创建副本。如果已将备份存储配置为异地冗余(GRS)，则价格将是此价格的 2 倍。每 GiB 价格保持不变。
                   </td>
                  </tr>
                 </table>
                </div>
               </div>
               <!-- END: TAB-CONTAINER-2 -->
               <!-- BEGIN: TAB-CONTAINER-3 -->
               <div class="tab-panel" id="tabContent9">
                <!-- -->
                <h3>
                  内存优化
                 </h3>
                 <p>
                  高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。
                 </p>
                <div class="scroll-table" style="display: block;">
                 <h3>
                  Esv3 系列
                 </h3>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-memory-esv3" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     VCORES
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     即用即付
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    E2s v3
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    16 GiB
                   </td>
                   <td>
                    ￥ 2.4 /小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E4s v3
                   </td>
                   <td>
                    4
                   </td>
                   <td>
                    32 GiB
                   </td>
                   <td>
                    ￥ 4.8 /小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E8s v3
                   </td>
                   <td>
                    8
                   </td>
                   <td>
                    64 GiB
                   </td>
                   <td>
                    ￥ 9.6 /小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E16s v3
                   </td>
                   <td>
                    16
                   </td>
                   <td>
                    128 GiB
                   </td>
                   <td>
                    ￥ 19.21 /小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E32s v3
                   </td>
                   <td>
                    32
                   </td>
                   <td>
                    256 GiB
                   </td>
                   <td>
                    ￥ 38.42 /小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E48s v3
                   </td>
                   <td>
                    48
                   </td>
                   <td>
                    384 GiB
                   </td>
                   <td>
                    ￥ 57.64 /小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E64s v3
                   </td>
                   <td>
                    64
                   </td>
                   <td>
                    504 GiB
                   </td>
                   <td>
                    ￥ 76.85 /小时
                   </td>
                  </tr>
                 </table>
                </div>
                <div class="scroll-table" style="display: block;">
                 <h3>
                  Edsv4 系列
                 </h3>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-memory-edsv4" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     vCores
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     即用即付
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    E2ds v4
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    16 GiB
                   </td>
                   <td>
                    ¥ 2.5/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E4ds v4
                   </td>
                   <td>
                    4
                   </td>
                   <td>
                    32 GiB
                   </td>
                   <td>
                    ¥ 5/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E8ds v4
                   </td>
                   <td>
                    8
                   </td>
                   <td>
                    64 GiB
                   </td>
                   <td>
                    ¥ 10/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E16ds v4
                   </td>
                   <td>
                    16
                   </td>
                   <td>
                    128 GiB
                   </td>
                   <td>
                    ¥ 20/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E20ds v4
                   </td>
                   <td>
                    20
                   </td>
                   <td>
                    160 GiB
                   </td>
                   <td>
                    ¥  25/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E32ds v4
                   </td>
                   <td>
                    32
                   </td>
                   <td>
                    256 GiB
                   </td>
                   <td>
                    ¥ 40/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E48ds v4
                   </td>
                   <td>
                    48
                   </td>
                   <td>
                    384 GiB
                   </td>
                   <td>
                    ¥ 60/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E64ds v4
                   </td>
                   <td>
                    64
                   </td>
                   <td>
                    504 GiB
                   </td>
                   <td>
                    ¥ 80/小时
                   </td>
                  </tr>
                 </table>
                </div>

                <!-- Edsv5 系列 -->
                <div class="scroll-table" style="display: block;">
                 <h3>Edsv5 系列</h3>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-memory-edsv5" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     vCore 数
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     即用即付
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    E2ds v5
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    16 GB
                   </td>
                   <td>
                    ￥2.49/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E4ds v5
                   </td>
                   <td>
                    4
                   </td>
                   <td>
                    32 GB
                   </td>
                   <td>
                    ￥4.99/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E8ds v5
                   </td>
                   <td>
                    8
                   </td>
                   <td>
                    64 GB
                   </td>
                   <td>
                    ￥9.97/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E16ds v5
                   </td>
                   <td>
                    16
                   </td>
                   <td>
                    128 GB
                   </td>
                   <td>
                    ￥19.94/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E32ds v5
                   </td>
                   <td>
                    32
                   </td>
                   <td>
                    256 GB
                   </td>
                   <td>
                    ￥39.89/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E48ds v5
                   </td>
                   <td>
                    48
                   </td>
                   <td>
                    384 GB
                   </td>
                   <td>
                    ￥59.83/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E64ds v5
                   </td>
                   <td>
                    64
                   </td>
                   <td>
                    504 GB
                   </td>
                   <td>
                    ￥79.78/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E96ds v5
                   </td>
                   <td>
                    96
                   </td>
                   <td>
                    768 GB
                   </td>
                   <td>
                    ￥119.67/小时
                   </td>
                  </tr>
                 </table>
                </div>
                <div class="scroll-table" style="display: block;">
                 <h3>
                  存储
                 </h3>
                 <p>
                  需要为你为服务器配置的存储付费。存储最多可预配为 16 TiB
                 </p>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-memory-storage" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    GiB/月
                   </td>
                   <td>
                    ¥ 1.17024
                   </td>
                  </tr>
                 </table>
                </div>
                <!-- <h3>Storage - Additional IOPS</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <th align="left"><strong></strong></th>
                                        <th align="left"><strong>价格</strong></th>
                                    </tr>
                                    <tr>
                                        <td>GB/月</td>
                                        <td>¥ 0.3528 </td>
                                    </tr>
                                </table> -->
                <div class="scroll-table" style="display: block;">
                 <h3>
                  备份存储
                 </h3>
                 <p>
                  备份存储是与服务器自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GiB/月收费。
                 </p>
                 <table cellpadding="0" cellspacing="0" id="postgresql-flexible-memory-backstorage" width="100%">
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     注意
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    GiB/月
                   </td>
                   <td>
                    ¥ 0.96672
                   </td>
                   <td>
                    GRS 需要两倍的存储容量，因为它会创建副本。如果已将备份存储配置为异地冗余(GRS)，则价格将是此价格的 2 倍。每 GiB 价格保持不变。
                   </td>
                  </tr>
                 </table>
                </div>
               </div>
               <!-- END: TAB-CONTAINER-3 -->
              </div>
             </div>
            </div>
           </div>
           
          </div>
         </div>
        </div>
       </div>


       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question1">
              我的账单是如何计算的？
            </a>
            <section>
             <p>
              灵活服务器按可预测的每小时费率计费。需要按预配的计算和存储以及使用的备份存储收取费用。账单上列有用于计算、存储和备份存储的单独的行项。计算按每 vCore-小时计费。存储和备份存储按每 GiB-月计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question2">
              备份存储如何计费？
            </a>
            <section>
             <p>
              备份存储是与服务器的自动备份关联的存储，包括数据和日志备份。增长备份保留期会使服务器使用的备份存储空间增大。如果主服务器的备份存储未超过 100% 的总预配存储，则无需额外付费。超出此部分的其他备份存储空间按 GiB/月收费。例如，如果主服务器的存储大小为 512 GiB，则可以获得 512 GiB 的备份，无需额外付费。但是，如果备份为 612 GiB，则需要为增加的 100 GiB 付费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question3">
              如果服务器的活动状态少于一小时或使用其他计算层少于一小时，会怎样？
            </a>
            <section>
             <p>
              需要为服务器存在的每个整小时付费，无论服务器是否在整个小时内处于活动状态都是如此。如果已纵向扩展数据库，将按该小时内使用的较高的 vCore 数计费。如果已在两个计算层之间切换，将按价格较高的计算层计费。
             </p>
             <p>
              例如：
             </p>
             <ul>
              <li>
                如果创建一个服务器并在 5 分钟后删除它，则按照预配的计算和存储空间收取一整个小时的费用。
              </li>
              <li>
                如果创建一个具有 8 个 vCore 常规用途计算的服务器，然后立即纵向扩展到 16 个 vCore 的常规用途计算，我们会对第一个小时的 16 个 vCore 常规用途计算计费。
              </li>
              <li>如果创建具有 8 个 vCore 的常规用途计算的服务器，然后立即将其升级到具有 8 个 vCore 的内存优化计算，我们将对第一个小时的 8 个 vCore 内存优化计算计费。</li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question4">
              “停止/启动”功能如何计费？
            </a>
            <section>
             <p>
              服务器停止时，只会对已预配的存储和已使用的超过主要预配存储卷 100% 的备份存储计费。服务器停止运行时，不会向你收取计算费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question5">
              是否需要承担任何网络数据传输费用？
            </a>
            <section>
             <p>
              可以。网络数据出口按标准网络费用计费。请参阅<a href="https://www.azure.cn/pricing/details/data-transfer/index.html" target="_blank">此处</a>了解详细信息。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="mysql-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
          Azure Database for PostgreSQL提供99.99%的运行时间服务级别协议，保证服务的高可用和用户业务的连续性。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="https://www.azure.cn/support/sla/postgresql/index.html" id="pricing_mysql_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h3>支持和服务级别协议</h3>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h3>服务热线：</h3>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="lfP2ZWJzeTqQsfzmkJEtLc0KbgEJ4HmKlYO6nv-5JTyHbcJcBMyeQaxo-KFvrMEQlJFtk1Kx-eY9lphbEc2hlQ6PvuDjDxHrEL3sd94QONE1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
